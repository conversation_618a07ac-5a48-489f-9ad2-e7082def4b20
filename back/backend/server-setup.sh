#!/bin/bash

# Server setup script for Contabo deployment
# This script should be run on the server to install all necessary dependencies

set -e  # Exit on any error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_header() {
    echo -e "${BLUE}[SETUP]${NC} $1"
}

print_header "🚀 Setting up Contabo server for NestJS deployment..."

# Update system packages
print_status "Updating system packages..."
apt update && apt upgrade -y

# Install essential packages
print_status "Installing essential packages..."
apt install -y curl wget git build-essential software-properties-common

# Install Node.js 18.x
print_status "Installing Node.js 18.x..."
if ! command -v node &> /dev/null; then
    curl -fsSL https://deb.nodesource.com/setup_18.x | bash -
    apt-get install -y nodejs
    print_status "Node.js installed: $(node --version)"
else
    print_status "Node.js already installed: $(node --version)"
fi

# Install PM2 globally
print_status "Installing PM2 process manager..."
if ! command -v pm2 &> /dev/null; then
    npm install -g pm2
    print_status "PM2 installed: $(pm2 --version)"
else
    print_status "PM2 already installed: $(pm2 --version)"
fi

# Install MongoDB
print_status "Installing MongoDB..."
if ! command -v mongod &> /dev/null; then
    # Import MongoDB public GPG key
    curl -fsSL https://pgp.mongodb.com/server-7.0.asc | gpg -o /usr/share/keyrings/mongodb-server-7.0.gpg --dearmor
    
    # Create MongoDB list file
    echo "deb [ arch=amd64,arm64 signed-by=/usr/share/keyrings/mongodb-server-7.0.gpg ] https://repo.mongodb.org/apt/ubuntu jammy/mongodb-org/7.0 multiverse" | tee /etc/apt/sources.list.d/mongodb-org-7.0.list
    
    # Update package database
    apt-get update
    
    # Install MongoDB
    apt-get install -y mongodb-org
    
    # Start and enable MongoDB
    systemctl start mongod
    systemctl enable mongod
    
    print_status "MongoDB installed and started"
else
    print_status "MongoDB already installed"
    systemctl start mongod
fi

# Install Redis
print_status "Installing Redis..."
if ! command -v redis-server &> /dev/null; then
    apt install -y redis-server
    
    # Configure Redis to start on boot
    systemctl enable redis-server
    systemctl start redis-server
    
    print_status "Redis installed and started"
else
    print_status "Redis already installed"
    systemctl start redis-server
fi

# Install Nginx (optional, for reverse proxy)
print_status "Installing Nginx..."
if ! command -v nginx &> /dev/null; then
    apt install -y nginx
    systemctl enable nginx
    systemctl start nginx
    print_status "Nginx installed and started"
else
    print_status "Nginx already installed"
fi

# Create application directory
print_status "Creating application directory..."
mkdir -p /var/www/superup-backend
chown -R $USER:$USER /var/www/superup-backend

# Configure firewall (if ufw is available)
if command -v ufw &> /dev/null; then
    print_status "Configuring firewall..."
    ufw allow ssh
    ufw allow 3000/tcp  # NestJS app
    ufw allow 80/tcp    # HTTP
    ufw allow 443/tcp   # HTTPS
    ufw --force enable
fi

# Create systemd service for the application (alternative to PM2)
print_status "Creating systemd service..."
cat > /etc/systemd/system/superup-backend.service << EOF
[Unit]
Description=SuperUp Backend NestJS Application
After=network.target

[Service]
Type=simple
User=root
WorkingDirectory=/var/www/superup-backend
Environment=NODE_ENV=production
ExecStart=/usr/bin/node dist/src/main.js
Restart=on-failure
RestartSec=10
StandardOutput=syslog
StandardError=syslog
SyslogIdentifier=superup-backend

[Install]
WantedBy=multi-user.target
EOF

systemctl daemon-reload

print_header "✅ Server setup completed!"
print_status "Services status:"
echo "  - MongoDB: $(systemctl is-active mongod)"
echo "  - Redis: $(systemctl is-active redis-server)"
echo "  - Nginx: $(systemctl is-active nginx)"

print_warning "Next steps:"
echo "1. Deploy your application files to /var/www/superup-backend"
echo "2. Copy your environment file (.env.server) to /var/www/superup-backend/.env"
echo "3. Install application dependencies: cd /var/www/superup-backend && npm install"
echo "4. Start the application with PM2: pm2 start ecosystem.config.js"
echo "5. Save PM2 configuration: pm2 save && pm2 startup"

print_status "Server is ready for deployment!"
