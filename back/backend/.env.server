# Server Environment Configuration for Contabo Deployment
# Based on development environment but adapted for server deployment

# MongoDB Configuration
# Update this with your server's MongoDB connection
DB_URL="mongodb://localhost:27017/orbit"

# JWT Configuration
JWT_SECRET="HOM@21939330"
issuer="<EMAIL>"
audience="<EMAIL>"

# Application Configuration
NODE_ENV="production"
EDIT_MODE="false"
ignoreEnvFile="false"
PORT=3000

# Admin Panel Configuration
ControlPanelAdminPassword="HOM@21939330"
ControlPanelAdminPasswordViewer="HOM@21939330"

# Push Notification Configuration
isOneSignalEnabled="false"
isFirebaseFcmEnabled="true"

# OneSignal Configuration (if needed)
oneSignalAppId="************************************"
oneSignalApiKey="os_v2_app_hidtuivxmzhr3axkgux4tsfwn6ltqhi5umyeqc44flara6kba3pfxjlcr255auhtclw3363ub6gbvmiyif76qqwyluwdosxm7bgtdey"

# Email Configuration
EMAIL_HOST="lim112.truehost.cloud"
EMAIL_USER="<EMAIL>"
EMAIL_PASSWORD="BulTWN(MN{pY"

# Agora Configuration
AGORA_APP_ID="********************************"
AGORA_APP_CERTIFICATE="********************************"

# Runtime Configuration
INLINE_RUNTIME_CHUNK="false"

# Apple Push Notification Configuration (if needed)
# apnKeyId=YOUR_KEY_ID
# apnAppBundle=YOUR_APPLE_BUNDLE
# appleAccountTeamId=YOUR_APPLE_TEAM_ID

# Redis Configuration (for WebSocket adapter)
# Add Redis configuration if using external Redis server
# REDIS_HOST=localhost
# REDIS_PORT=6379
# REDIS_PASSWORD=your_redis_password
