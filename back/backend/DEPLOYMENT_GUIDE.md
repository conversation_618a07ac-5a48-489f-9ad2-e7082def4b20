# SuperUp Backend Deployment Guide for Contabo Server

## Prerequisites
- SSH access to your Contabo server (root@************)
- Local development environment with Node.js and npm

## Step 1: Prepare Your Server

First, connect to your Contabo server and run the server setup script:

```bash
# Connect to your server
ssh root@************

# Download and run the server setup script
# (You'll need to transfer the server-setup.sh file to your server first)
chmod +x server-setup.sh
./server-setup.sh
```

Alternatively, you can run the setup commands manually:

```bash
# Update system
apt update && apt upgrade -y

# Install Node.js 18.x
curl -fsSL https://deb.nodesource.com/setup_18.x | bash -
apt-get install -y nodejs

# Install PM2
npm install -g pm2

# Install MongoDB
curl -fsSL https://pgp.mongodb.com/server-7.0.asc | gpg -o /usr/share/keyrings/mongodb-server-7.0.gpg --dearmor
echo "deb [ arch=amd64,arm64 signed-by=/usr/share/keyrings/mongodb-server-7.0.gpg ] https://repo.mongodb.org/apt/ubuntu jammy/mongodb-org/7.0 multiverse" | tee /etc/apt/sources.list.d/mongodb-org-7.0.list
apt-get update
apt-get install -y mongodb-org
systemctl start mongod
systemctl enable mongod

# Install Redis
apt install -y redis-server
systemctl enable redis-server
systemctl start redis-server

# Install Nginx (optional)
apt install -y nginx
systemctl enable nginx
systemctl start nginx

# Create application directory
mkdir -p /var/www/superup-backend
```

## Step 2: Deploy Your Application

From your local machine, run the deployment script:

```bash
# Make sure you're in the backend directory
cd /Users/<USER>/Desktop/source/back/backend

# Run the deployment script
./deploy.sh
```

Or deploy manually:

```bash
# Build the application locally
npm run build

# Create deployment package
tar --exclude='node_modules' --exclude='.git' --exclude='*.log' --exclude='.env.development' -czf deployment.tar.gz .

# Transfer to server
scp deployment.tar.gz root@************:/var/www/superup-backend/

# Connect to server and extract
ssh root@************
cd /var/www/superup-backend
tar -xzf deployment.tar.gz
rm deployment.tar.gz

# Install dependencies
npm install --production
npm install --save-dev @types/multer
npm install -g @nestjs/cli typescript@4.8.4 cross-env
```

## Step 3: Configure Environment

On your server, set up the environment file:

```bash
# Copy the server environment file
cp .env.server .env

# Edit the environment file if needed
nano .env
```

Make sure to update the MongoDB URL and other configurations as needed for your server environment.

## Step 4: Start the Application

Using PM2 (recommended):

```bash
# Start the application with PM2
pm2 start ecosystem.config.js

# Save PM2 configuration
pm2 save

# Set up PM2 to start on boot
pm2 startup
# Follow the instructions provided by the command above
```

Or using systemd:

```bash
# Enable and start the systemd service
systemctl enable superup-backend
systemctl start superup-backend

# Check status
systemctl status superup-backend
```

## Step 5: Configure Nginx (Optional)

If you want to use Nginx as a reverse proxy:

```bash
# Copy the nginx configuration
cp nginx.conf /etc/nginx/sites-available/superup-backend

# Enable the site
ln -s /etc/nginx/sites-available/superup-backend /etc/nginx/sites-enabled/

# Test nginx configuration
nginx -t

# Reload nginx
systemctl reload nginx
```

## Step 6: Verify Deployment

Check if your application is running:

```bash
# Check if the application is listening on port 3000
netstat -tlnp | grep :3000

# Check PM2 status
pm2 status

# Check application logs
pm2 logs

# Test the API
curl http://localhost:3000
# or if using nginx
curl http://************
```

## Troubleshooting

### Common Issues:

1. **MongoDB Connection Issues:**
   ```bash
   # Check MongoDB status
   systemctl status mongod
   
   # Check MongoDB logs
   tail -f /var/log/mongodb/mongod.log
   ```

2. **Redis Connection Issues:**
   ```bash
   # Check Redis status
   systemctl status redis-server
   
   # Test Redis connection
   redis-cli ping
   ```

3. **Application Not Starting:**
   ```bash
   # Check PM2 logs
   pm2 logs
   
   # Check if all dependencies are installed
   cd /var/www/superup-backend
   npm list
   ```

4. **Port Already in Use:**
   ```bash
   # Check what's using port 3000
   lsof -i :3000
   
   # Kill the process if needed
   kill -9 <PID>
   ```

## Environment Variables to Update

Make sure to update these in your `.env` file on the server:

- `DB_URL`: Update to match your server's MongoDB setup
- `NODE_ENV`: Set to "production" for production deployment
- `PORT`: Ensure it matches your desired port (default: 3000)

## Security Considerations

1. Configure firewall rules
2. Set up SSL/TLS certificates if using a domain
3. Secure MongoDB with authentication
4. Configure Redis with password protection
5. Regular security updates

## Monitoring

Set up monitoring for your application:

```bash
# Monitor with PM2
pm2 monit

# Set up log rotation
pm2 install pm2-logrotate
```

Your application should now be accessible at `http://************:3000` (or port 80 if using Nginx).
