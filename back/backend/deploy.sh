#!/bin/bash

# Deployment script for NestJS backend to Contabo server
# Usage: ./deploy.sh

set -e  # Exit on any error

# Configuration
SERVER_USER="root"
SERVER_HOST="************"
SERVER_PATH="/var/www/superup-backend"
LOCAL_PATH="."

echo "🚀 Starting deployment to Contabo server..."

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Test SSH connection
print_status "Testing SSH connection to server..."
if ! ssh -o ConnectTimeout=10 -o BatchMode=yes $SERVER_USER@$SERVER_HOST exit 2>/dev/null; then
    print_error "Cannot connect to server. Please check your SSH connection."
    exit 1
fi

print_status "SSH connection successful!"

# Create server directory structure
print_status "Setting up server directory structure..."
ssh $SERVER_USER@$SERVER_HOST "
    mkdir -p $SERVER_PATH
    mkdir -p $SERVER_PATH/logs
    mkdir -p $SERVER_PATH/public
    mkdir -p $SERVER_PATH/public/media
"

# Build the application locally
print_status "Building application locally..."
npm run build

# Create deployment package (excluding node_modules and other unnecessary files)
print_status "Creating deployment package..."
tar --exclude='node_modules' \
    --exclude='.git' \
    --exclude='*.log' \
    --exclude='.env.development' \
    --exclude='deploy.sh' \
    -czf deployment.tar.gz .

# Transfer files to server
print_status "Transferring files to server..."
scp deployment.tar.gz $SERVER_USER@$SERVER_HOST:$SERVER_PATH/

# Extract and setup on server
print_status "Setting up application on server..."
ssh $SERVER_USER@$SERVER_HOST "
    cd $SERVER_PATH
    tar -xzf deployment.tar.gz
    rm deployment.tar.gz
    
    # Install Node.js if not present
    if ! command -v node &> /dev/null; then
        echo 'Installing Node.js...'
        curl -fsSL https://deb.nodesource.com/setup_18.x | sudo -E bash -
        apt-get install -y nodejs
    fi
    
    # Install PM2 globally if not present
    if ! command -v pm2 &> /dev/null; then
        echo 'Installing PM2...'
        npm install -g pm2
    fi
    
    # Install dependencies
    echo 'Installing dependencies...'
    npm install --production
    
    # Install additional required packages
    npm install --save-dev @types/multer
    npm install -g @nestjs/cli
    npm install -g typescript@4.8.4
    npm install -g cross-env
"

# Clean up local deployment file
rm deployment.tar.gz

print_status "Deployment package transferred and extracted successfully!"
print_warning "Next steps:"
echo "1. Configure environment variables on the server"
echo "2. Set up MongoDB and Redis on the server"
echo "3. Start the application with PM2"
echo ""
echo "To complete the deployment, run:"
echo "ssh $SERVER_USER@$SERVER_HOST"
echo "cd $SERVER_PATH"
echo "pm2 start ecosystem.config.js"

print_status "Deployment script completed!"
