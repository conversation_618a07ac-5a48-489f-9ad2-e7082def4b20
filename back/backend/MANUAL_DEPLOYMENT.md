# Manual Deployment Instructions for Contabo Server

## Quick Deployment Steps

Your application has been built and packaged. Follow these steps to deploy:

### 1. Transfer Files to Server

```bash
# From your local machine, transfer the deployment package
scp deployment.tar.gz root@************:/tmp/

# Also transfer the server setup script
scp server-setup.sh root@************:/tmp/

# Transfer the environment file
scp .env.server root@************:/tmp/
```

### 2. Connect to Your Server

```bash
ssh root@************
```

### 3. Set Up Server Environment

```bash
# Run the server setup script
cd /tmp
chmod +x server-setup.sh
./server-setup.sh
```

### 4. Deploy Application

```bash
# Create application directory
mkdir -p /var/www/superup-backend
cd /var/www/superup-backend

# Extract deployment package
tar -xzf /tmp/deployment.tar.gz

# Copy environment file
cp /tmp/.env.server .env

# Install dependencies
npm install --production
npm install --save-dev @types/multer
npm install -g @nestjs/cli typescript@4.8.4 cross-env
```

### 5. Start Application

```bash
# Start with PM2
pm2 start ecosystem.config.js

# Save PM2 configuration
pm2 save

# Set up PM2 to start on boot
pm2 startup
# Follow the instructions provided by the command
```

### 6. Verify Deployment

```bash
# Check if application is running
pm2 status

# Check logs
pm2 logs

# Test the API
curl http://localhost:3000

# Check if port is listening
netstat -tlnp | grep :3000
```

## Environment Configuration

Your `.env` file is configured with development settings. You may need to update:

1. **MongoDB URL**: Currently set to `mongodb://localhost:27017/orbit`
2. **Node Environment**: Set to `production`
3. **Port**: Set to `3000`

## Services Status Check

After setup, verify all services are running:

```bash
# Check MongoDB
systemctl status mongod

# Check Redis
systemctl status redis-server

# Check your application
pm2 status
```

## Troubleshooting

If you encounter issues:

1. **Check logs**: `pm2 logs`
2. **Restart application**: `pm2 restart all`
3. **Check MongoDB**: `systemctl status mongod`
4. **Check Redis**: `redis-cli ping`

## Access Your Application

Once deployed, your application will be accessible at:
- **Direct access**: `http://************:3000`
- **With Nginx** (if configured): `http://************`

## Next Steps

1. Set up SSL certificate for HTTPS
2. Configure domain name (if you have one)
3. Set up monitoring and logging
4. Configure automated backups

Your deployment package is ready at: `/Users/<USER>/Desktop/source/back/backend/deployment.tar.gz`
